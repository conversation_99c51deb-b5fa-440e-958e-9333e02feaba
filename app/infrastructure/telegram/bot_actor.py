"""
Bot Actor for handling Telegram bot operations through the event bus.

This actor wraps the TelegramBot functionality, handles message polling,
and sends messages through event-driven communication.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from aiogram import Bo<PERSON>, Dispatcher
from aiogram.filters import Command
from aiogram.types import Message

from app.core.actors.actor import Actor
from app.core.events.event_bus import event_bus
from app.core.context.message_context import message_context_manager, MessageState
from app.config import settings
from app.config.prompts import SYSTEM_PROMPTS
from app.core.events.events import (
    UserMessageReceived, SendMessageRequest, MessageSent,
    GenericResponse, ErrorOccurred
)

logger = logging.getLogger(__name__)


class BotActor(Actor):
    """Actor responsible for Telegram bot operations."""
    
    def __init__(self, name: Optional[str] = None):
        super().__init__(name or "BotActor")
        self.bot = None
        self.dp = None
        self.user_sessions: Dict[int, datetime] = {}
        self._polling_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> None:
        """Initialize the bot actor."""
        try:
            logger.info(f"🔧 Initializing {self.name}")

            # Initialize bot and dispatcher
            self.bot = Bot(token=settings.telegram.bot_token)
            self.dp = Dispatcher()

            # Register handlers
            self._register_handlers()

            logger.info(f"✅ {self.name} initialized successfully")

        except Exception as e:
            logger.error(f"💥 Failed to initialize {self.name}: {e}")
            raise

    async def start(self) -> None:
        """Start the bot actor and begin polling."""
        try:
            # Call parent start method to handle initialization
            await super().start()

            # Start polling for messages in a background task
            if self.dp and self.bot:
                logger.info(f"🔄 Starting polling for {self.name}")
                self._polling_task = asyncio.create_task(self.dp.start_polling(self.bot))
                logger.info(f"✅ Polling task created for {self.name}")

        except Exception as e:
            logger.error(f"💥 Failed to start polling for {self.name}: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup the bot actor."""
        try:
            logger.info(f"🧹 Cleaning up {self.name}")

            # Stop polling task
            if self._polling_task and not self._polling_task.done():
                self._polling_task.cancel()
                try:
                    await self._polling_task
                except asyncio.CancelledError:
                    logger.debug(f"Polling task cancelled for {self.name}")

            # Stop polling
            if self.dp:
                await self.dp.stop_polling()

            # Close bot session
            if self.bot:
                await self.bot.session.close()

            logger.info(f"✅ {self.name} cleanup completed")

        except Exception as e:
            logger.error(f"💥 Error during {self.name} cleanup: {e}")
    
    def _register_handlers(self):
        """Register message and command handlers."""
        if self.dp:
            self.dp.message.register(self.handle_start_command, Command("start"))
            self.dp.message.register(self.handle_help_command, Command("help"))
            self.dp.message.register(self.handle_message)
    
    async def handle_start_command(self, message: Message):
        """Handle /start command."""
        try:
            if not message.from_user:
                logger.warning("Cannot handle start command: missing user information")
                return
            
            user_id = message.from_user.id
            
            # Track user session
            await self._start_user_session(message)
            
            # Send welcome message
            await message.reply(
                SYSTEM_PROMPTS["welcome_message"],
                parse_mode="Markdown"
            )
            
            logger.info(f"User {user_id} started session")
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await self._handle_error(message, "start_command", str(e))
    
    async def handle_help_command(self, message: Message):
        """Handle /help command."""
        try:
            await message.reply(
                SYSTEM_PROMPTS["help_message"],
                parse_mode="Markdown"
            )
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await self._handle_error(message, "help_command", str(e))
    
    async def handle_message(self, message: Message):
        """Handle incoming messages."""
        try:
            if not message.from_user:
                logger.warning("Cannot handle message: missing user information")
                return

            # Update user session
            self.user_sessions[message.from_user.id] = datetime.now()
            logger.info(message)
            # Create message context for tracking
            context = message_context_manager.create_context(
                telegram_message_id=message.message_id,
                chat_id=message.chat.id,
                message_text=message.text or "",
                telegram_user_id=message.from_user.id
            )

            # Create and publish user message received event with context_id
            user_message_event = UserMessageReceived(
                context_id=context.context_id,
                telegram_user_id=message.from_user.id,
                chat_id=message.chat.id,
                telegram_message_id=message.message_id,
                message_text=message.text or "",
                message_type=self._get_message_type(message),
                username=message.from_user.username,
                first_name=message.from_user.first_name,
                last_name=message.from_user.last_name,
                telegram_timestamp=message.date,
                chat_type=message.chat.type,
                chat_title=message.chat.title,
                is_reply=message.reply_to_message is not None,
                reply_to_message_id=message.reply_to_message.message_id if message.reply_to_message else None
            )

            # Publish the event for preprocessing
            await event_bus.publish("user_message_received", user_message_event)

            logger.debug(f"Published message from user {message.from_user.id} with context_id {context.context_id}")

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self._handle_error(message, "handle_message", str(e))
    
    @event_bus.subscribe("send_message_request")
    async def handle_send_message_request(self, event_data: SendMessageRequest) -> None:
        """Handle requests to send messages."""
        try:
            # Get context if context_id is provided
            context = None
            if hasattr(event_data, 'context_id') and event_data.context_id:
                context = message_context_manager.get_context(event_data.context_id)

            sent_message = None
            # Send the message
            if self.bot:
                sent_message = await self.bot.send_message(
                    chat_id=event_data.chat_id,
                    text=event_data.message_text,
                    reply_to_message_id=event_data.reply_to_message_id,
                    parse_mode=event_data.parse_mode
                )

                # Update context if available
                if context:
                    context.update_state(
                        MessageState.RESPONDING,
                        self.name,
                        {"bot_message_id": sent_message.message_id}
                    )

                # Publish message sent event with context_id
                message_sent_event = MessageSent(
                    chat_id=event_data.chat_id,
                    telegram_message_id=sent_message.message_id,
                    message_text=event_data.message_text,
                    success=True
                )
                if hasattr(event_data, 'context_id'):
                    message_sent_event.context_id = event_data.context_id

                await event_bus.publish("message_sent", message_sent_event)

                # Store the bot message in database with context_id
                store_request = {
                    "telegram_message_id": sent_message.message_id,
                    "chat_id": event_data.chat_id,
                    "telegram_user_id": None,  # Bot message
                    "message_text": event_data.message_text,
                    "message_type": "text",
                    "is_from_bot": True,
                    "telegram_timestamp": sent_message.date,
                    "is_reply": event_data.reply_to_message_id is not None,
                    "reply_to_message_id": event_data.reply_to_message_id,
                    "chat_type": "private",  # Simplified for now
                    "chat_title": None
                }
                if hasattr(event_data, 'context_id'):
                    store_request["context_id"] = event_data.context_id

                await event_bus.publish("store_message_request", store_request)

                logger.debug(f"Sent message to chat {event_data.chat_id}")
            else:
                logger.error("Bot is not initialized, cannot send message")
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            
            # Publish failed message event
            await event_bus.publish("message_sent", MessageSent(
                chat_id=event_data.chat_id,
                telegram_message_id=0,
                message_text=event_data.message_text,
                success=False,
                error_message=str(e)
            ))
    
    @event_bus.subscribe("generic_response")
    async def handle_generic_response(self, event_data: GenericResponse) -> None:
        """Handle generic response requests."""
        try:
            response_text = self._get_generic_response_text(event_data.message_type)
            
            # Send the response
            await event_bus.publish("send_message_request", SendMessageRequest(
                chat_id=event_data.chat_id,
                message_text=response_text,
                reply_to_message_id=event_data.reply_to_message_id
            ))
            
        except Exception as e:
            logger.error(f"Error handling generic response: {e}")
    
    def _get_generic_response_text(self, message_type: str) -> str:
        """Get the appropriate generic response text."""
        responses = {
            "new_user": """Hello! Welcome to Zeitwahl, your AI scheduling assistant.

I notice you're new here. To provide you with the best assistance, I'd like to know your timezone.

Please tell me:
1. What timezone are you in? (e.g., "UTC", "America/New_York", "Europe/London")
2. How would you like me to help you with scheduling and time management?

You can also type /help to see what I can do for you.""",
            
            "irrelevant_message": """I'm Zeitwahl, your AI scheduling and time management assistant. 

I specialize in helping with:
• Calendar management and scheduling
• Time zone conversions  
• Meeting planning and coordination
• Productivity and time management advice
• Date and time calculations

Your message doesn't seem to be related to scheduling or time management. How can I help you with your calendar or time-related needs today?""",
            
            "error": "I apologize, but I encountered an error processing your message. Please try again or contact support if the issue persists."
        }
        
        return responses.get(message_type, responses["error"])
    
    def _get_message_type(self, message: Message) -> str:
        """Determine the type of message."""
        if message.text:
            return "text"
        elif message.photo:
            return "photo"
        elif message.document:
            return "document"
        elif message.voice:
            return "voice"
        elif message.video:
            return "video"
        elif message.sticker:
            return "sticker"
        else:
            return "other"
    
    async def _start_user_session(self, message: Message):
        """Start a user session."""
        if message.from_user:
            user_id = message.from_user.id
            self.user_sessions[user_id] = datetime.now()

            # Publish user session started event
            await event_bus.publish("user_session_started", {
                "user_id": user_id,
                "chat_id": message.chat.id,
                "username": message.from_user.username,
                "first_name": message.from_user.first_name,
                "last_name": message.from_user.last_name,
                "timestamp": datetime.now()
            })
    
    async def _handle_error(self, message: Message, operation: str, error_message: str):
        """Handle errors and publish error events."""
        try:
            # Publish error event
            await event_bus.publish("error_occurred", ErrorOccurred(
                user_id=message.from_user.id if message.from_user else None,
                chat_id=message.chat.id,
                message_id=message.message_id,
                error_type="bot_error",
                error_message=error_message,
                component=f"{self.name}.{operation}"
            ))
            
            # Send user-friendly error message
            await message.reply(SYSTEM_PROMPTS["error_messages"]["general_error"])
            
        except Exception as e:
            logger.error(f"Failed to handle error: {e}")
            # If we can't send error message, just log it
